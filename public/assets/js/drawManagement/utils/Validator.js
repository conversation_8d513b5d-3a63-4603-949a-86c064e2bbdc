/**
 * Client-side Validation Utility
 *
 * Provides comprehensive validation for draw management data
 * before sending to the backend
 */
class Validator {
    constructor() {
        this.errors = [];
    }

    /**
     * Validate category data
     *
     * @param {Object} category Category object
     * @param {Object} context Validation context
     * @returns {boolean} True if valid
     */
    validateCategory(category, context = {}) {
        this.clearErrors();

        // Required fields
        if (!category.categoryName || category.categoryName.trim() === '') {
            this.addError('Category name is required');
        }

        // Category name length
        if (category.categoryName && category.categoryName.length > 255) {
            this.addError('Category name must not exceed 255 characters');
        }

        // Description length
        if (category.description && category.description.length > 1000) {
            this.addError('Category description must not exceed 1000 characters');
        }

        // Order validation
        if (!category.order || category.order <= 0) {
            this.addError('Category order must be a positive number');
        }

        // Max categories check
        if (context.maxCategories && category.order > context.maxCategories) {
            this.addError(`Category order cannot exceed ${context.maxCategories}`);
        }

        // Validate line items if present
        if (category.lineItems && Array.isArray(category.lineItems)) {
            category.lineItems.forEach((lineItem, index) => {
                if (!this.validateLineItem(lineItem, context)) {
                    this.addError(`Line item ${index + 1}: ${this.getFirstError()}`);
                }
            });
        }

        return this.isValid();
    }

    /**
     * Validate line item data
     *
     * @param {Object} lineItem Line item object
     * @param {Object} context Validation context
     * @returns {boolean} True if valid
     */
    validateLineItem(lineItem, context = {}) {
        this.clearErrors();

        // Required fields
        if (!lineItem.name || lineItem.name.trim() === '') {
            this.addError('Line item name is required');
        }

        // Name length
        if (lineItem.name && lineItem.name.length > 255) {
            this.addError('Line item name must not exceed 255 characters');
        }

        // Description length
        if (lineItem.description && lineItem.description.length > 1000) {
            this.addError('Line item description must not exceed 1000 characters');
        }

        // Order validation
        if (!lineItem.order || lineItem.order <= 0) {
            this.addError('Line item order must be a positive number');
        }

        //Validate Borrower Side Fields
        if (context.userType === 'borrower') {
            // Cost validation
            if (!context.isDraft && (!lineItem.cost || lineItem.cost <= 0)) {
                this.addError('Line item cost must be a positive number');
            }

            // Completed amount validation
            if (lineItem.completedAmount < 0) {
                this.addError('Completed amount cannot be negative');
            }

            if (lineItem.completedAmount > lineItem.cost) {
                this.addError('Completed amount cannot exceed total cost');
            }

            // Completed percent validation
            if (lineItem.completedPercent < 0 || lineItem.completedPercent > 100) {
                this.addError('Completed percent must be between 0 and 100');
            }

            // Requested amount validation
            if (lineItem.requestedAmount < 0) {
                this.addError('Requested amount cannot be negative');
            }

            const maxRequestable = lineItem.cost - lineItem.completedAmount;
            if (lineItem.requestedAmount > maxRequestable) {
                this.addError('Requested amount cannot exceed remaining cost');
            }

            // Requested percent validation
            if (lineItem.requestedPercent < 0) {
                this.addError('Requested percent cannot be negative');
            }

            const maxRequestablePercent = 100 - lineItem.completedPercent;
            if (lineItem.requestedPercent > maxRequestablePercent) {
                this.addError('Requested percent cannot exceed remaining percent');
            }

            // Consistency check between amount and percent
            if (lineItem.cost > 0) {
                const calculatedAmount = (lineItem.requestedPercent / 100) * lineItem.cost;
                const tolerance = 0.01;

                if (Math.abs(lineItem.requestedAmount - calculatedAmount) > tolerance) {
                    this.addError('Requested amount and percent are inconsistent');
                }
            }

            // Notes length validation
            if (lineItem.notes && lineItem.notes.length > 2000) {
                this.addError('Notes must not exceed 2000 characters');
            }

            if (lineItem.lenderNotes && lineItem.lenderNotes.length > 2000) {
                this.addError('Lender notes must not exceed 2000 characters');
            }

        }

        return this.isValid();
    }

    /**
     * Validate draw request data
     *
     * @param {Object} drawRequest Draw request object
     * @param {Object} context Validation context
     * @returns {boolean} True if valid
     */
    validateDrawRequest(drawRequest, context = {}) {
        this.clearErrors();

        // Required fields
        if (!drawRequest.LMRId) {
            this.addError('Loan file ID is required');
        }

        if (!drawRequest.status) {
            this.addError('Status is required');
        }

        // Status validation
        const validStatuses = ['new', 'pending', 'approved', 'rejected'];
        if (drawRequest.status && !validStatuses.includes(drawRequest.status)) {
            this.addError('Invalid status value');
        }

        // Amount validation
        if (drawRequest.amountRequested < 0) {
            this.addError('Requested amount cannot be negative');
        }

        if (drawRequest.amountApproved && drawRequest.amountApproved < 0) {
            this.addError('Approved amount cannot be negative');
        }

        if (drawRequest.amountApproved > drawRequest.amountRequested) {
            this.addError('Approved amount cannot exceed requested amount');
        }

        // Rejection reason validation
        if (drawRequest.status === 'rejected' && (!drawRequest.rejectionReason || drawRequest.rejectionReason.trim() === '')) {
            this.addError('Rejection reason is required when status is rejected');
        }

        // Lender notes length
        if (drawRequest.lenderNotes && drawRequest.lenderNotes.length > 2000) {
            this.addError('Lender notes must not exceed 2000 characters');
        }

        // Validate each category
        if (drawRequest.categories) {
            drawRequest.categories.forEach((category, index) => {
                if (!this.validateCategory(category, context)) {
                    this.addError(`Category ${index + 1}: ${this.getFirstError()}`);
                }
            });
        }

        return this.isValid();
    }

    /**
     * Validate form data before submission
     *
     * @param {Object} formData Form data object
     * @param {string} formType Type of form (categories, lineItems, drawRequest)
     * @param {Object} context Validation context
     * @returns {boolean} True if valid
     */
    validateForm(formData, formType, context = {}) {
        this.clearErrors();

        switch (formType) {
            case 'categories':
                return this.validateCategoriesForm(formData, context);
            case 'lineItems':
                return this.validateLineItemsForm(formData, context);
            case 'drawRequest':
                return this.validateDrawRequest(formData, context);
            default:
                this.addError('Unknown form type');
                return false;
        }
    }

    /**
     * Validate categories form data
     *
     * @param {Object} formData Form data
     * @param {Object} context Validation context
     * @returns {boolean} True if valid
     */
    validateCategoriesForm(formData, context) {
        if (!formData.categories || !Array.isArray(formData.categories)) {
            this.addError('Categories data is required');
            return false;
        }

        // Check for duplicate orders
        const orders = formData.categories.map(cat => cat.order);
        const duplicateOrders = orders.filter((order, index) => orders.indexOf(order) !== index);
        if (duplicateOrders.length > 0) {
            this.addError('Duplicate category orders found');
        }

        // Validate each category
        formData.categories.forEach((category, index) => {
            if (!this.validateCategory(category, context)) {
                this.addError(`Category ${index + 1}: ${this.getFirstError()}`);
            }
        });

        return this.isValid();
    }

    /**
     * Validate line items form data
     *
     * @param {Object} formData Form data
     * @param {Object} context Validation context
     * @returns {boolean} True if valid
     */
    validateLineItemsForm(formData, context) {
        if (!formData.lineItems || typeof formData.lineItems !== 'object') {
            this.addError('Line items data is required');
            return false;
        }

        let hasLineItems = false;
        let hasRequestedAmounts = false;
        // Validate each category's line items
        Object.keys(formData.lineItems).forEach(categoryId => {
            const categoryLineItems = formData.lineItems[categoryId];

            if (Array.isArray(categoryLineItems) && categoryLineItems.length > 0) {
                hasLineItems = true;

                categoryLineItems.forEach((lineItem, index) => {
                    if (!this.validateLineItem(lineItem, context)) {
                        this.addError(`Category ${categoryId}, Line item ${index + 1}: ${this.getFirstError()}`);
                    }

                    if (lineItem.requestedAmount == undefined || !lineItem.requestedAmount > 0) {
                        hasRequestedAmounts = true;
                    }
                });
            }
        });

        // For borrower submissions, require at least one requested amount
        if (context.userType === 'borrower' && !context.isDraft && !hasRequestedAmounts) {
            this.addError('At least one line item must have a requested amount');
        }

        return this.isValid();
    }

    /**
     * Add validation error
     *
     * @param {string} error Error message
     */
    addError(error) {
        this.errors.push(error);
    }

    /**
     * Get all validation errors
     *
     * @returns {Array} Array of error messages
     */
    getErrors() {
        return this.errors;
    }

    /**
     * Get first validation error
     *
     * @returns {string|null} First error message or null
     */
    getFirstError() {
        return this.errors.length > 0 ? this.errors[0] : null;
    }

    /**
     * Check if validation passed
     *
     * @returns {boolean} True if no errors
     */
    isValid() {
        return this.errors.length === 0;
    }

    /**
     * Clear validation errors
     */
    clearErrors() {
        this.errors = [];
    }

    /**
     * Display validation errors to user
     *
     * @param {string} containerSelector Selector for error container
     */
    displayErrors(containerSelector = '.validation-errors') {
        const $container = $(containerSelector);

        if (this.errors.length === 0) {
            $container.hide();
            return;
        }

        const errorHtml = this.errors.map(error => `<li>${error}</li>`).join('');
        $container.html(`<ul class="list-unstyled mb-0">${errorHtml}</ul>`).show();
    }
}

// Export for use in other modules
window.Validator = Validator;
