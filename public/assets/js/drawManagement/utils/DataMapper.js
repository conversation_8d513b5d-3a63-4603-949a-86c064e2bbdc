/**
 * Data Mapper Utility
 *
 * Provides consistent property mapping and data transformation
 * between frontend and backend data structures
 */
class DataMapper {
    /**
     * Property mapping configurations
     */
    static propertyMaps = {
        category: {
            frontend: ['id', 'categoryName', 'description', 'order', 'lineItems'],
            backend: ['id', 'categoryName', 'description', 'order', 'lineItems'],
            mapping: {} // Direct mapping for categories
        },
        lineItem: {
            frontend: ['id', 'categoryId', 'name', 'description', 'order', 'cost', 'completedAmount',
                      'completedPercent', 'requestedAmount', 'requestedPercent', 'notes', 'lenderNotes'],
            backend: ['id', 'categoryId', 'name', 'description', 'order', 'cost', 'completedAmount',
                     'completedPercent', 'requestedAmount', 'requestedPercent', 'notes', 'lenderNotes'],
            mapping: {} // Direct mapping for line items
        },
        drawRequest: {
            frontend: ['id', 'LMRId', 'status', 'categories', 'amountRequested', 'lenderNotes'],
            backend: ['id', 'LMRId', 'status', 'categories', 'amountRequested', 'lenderNotes'],
            mapping: {} // Direct mapping for draw requests
        }
    };

    /**
     * Map object properties from one structure to another
     *
     * @param {Object} sourceObject Source object
     * @param {string} objectType Type of object (category, lineItem, drawRequest)
     * @param {string} direction Direction of mapping (toBackend, toFrontend)
     * @returns {Object} Mapped object
     */
    static mapObject(sourceObject, objectType, direction = 'toBackend') {
        if (!sourceObject || typeof sourceObject !== 'object') {
            return {};
        }

        const config = this.propertyMaps[objectType];
        if (!config) {
            console.warn(`Unknown object type: ${objectType}`);
            return sourceObject;
        }

        const mappedObject = {};
        const sourceProperties = direction === 'toBackend' ? config.frontend : config.backend;
        const mapping = config.mapping;

        sourceProperties.forEach(property => {
            const mappedProperty = mapping[property] || property;

            if (sourceObject.hasOwnProperty(property)) {
                let value = sourceObject[property];

                // Handle nested objects
                if (property === 'lineItems' && Array.isArray(value)) {
                    value = value.map(item => this.mapObject(item, 'lineItem', direction));
                } else if (property === 'categories' && Array.isArray(value)) {
                    value = value.map(item => this.mapObject(item, 'category', direction));
                }

                // Apply type conversion
                value = this.convertValue(value, property, direction);

                mappedObject[mappedProperty] = value;
            }
        });

        return mappedObject;
    }

    /**
     * Convert value to appropriate type
     *
     * @param {*} value Value to convert
     * @param {string} property Property name
     * @param {string} direction Direction of conversion
     * @returns {*} Converted value
     */
    static convertValue(value, property, direction) {
        if (value === null || value === undefined) {
            return null;
        }

        // Handle numeric properties
        if (['id', 'order', 'LMRId'].includes(property)) {
            return value === '' ? null : parseInt(value, 10);
        }

        if (['cost', 'completedAmount', 'completedPercent', 'requestedAmount',
             'requestedPercent', 'amountRequested'].includes(property)) {
            return value === '' ? 0 : parseFloat(value);
        }

        // Handle string properties
        if (['categoryName', 'description', 'name', 'notes', 'lenderNotes', 'status'].includes(property)) {
            return String(value);
        }

        return value;
    }

    /**
     * Extract data from DOM elements for categories
     *
     * @param {jQuery} $container Container element
     * @returns {Array} Array of category objects
     */
    static extractCategoriesFromDOM($container) {
        const categories = [];

        $container.find('.category-item').each(function(index) {
            const $this = $(this);
            let id = $this.data('category-id');

            // Handle new category IDs
            if (typeof id === 'string' && id.startsWith('new_cat_')) {
                id = null;
            } else {
                id = parseInt(id, 10);
            }

            const category = {
                id: id,
                categoryName: $this.find('.category-name').text().trim(),
                description: $this.find('.category-description').text().trim(),
                order: index + 1,
                lineItems: []
            };

            categories.push(category);
        });

        return categories;
    }

    /**
     * Extract data from DOM elements for line items
     *
     * @param {jQuery} $container Container element
     * @returns {Object} Object with line items grouped by category
     */
    static extractLineItemsFromDOM($container) {
        const groupedLineItems = {};

        $container.find('.line-item-category-section').each(function() {
            const $categoryCard = $(this);
            const categoryId = $categoryCard.data('category-id');

            if (!categoryId) {
                return;
            }

            groupedLineItems[categoryId] = [];

            $categoryCard.find('.editable-line-item').each(function() {
                const $row = $(this);
                const lineItemId = $row.data('line-item-id');

                if (!lineItemId) {
                    return;
                }

                const lineItem = {
                    id: parseInt(lineItemId, 10),
                    categoryId: parseInt(categoryId, 10),
                    name: $row.find('.line-item-name-display').text().trim(),
                    cost: parseFloat($row.find('.line-item-cost-input').val() || 0),
                    completedAmount: parseFloat($row.find('.line-item-completed-amount-input').val() || 0),
                    completedPercent: parseFloat($row.find('.line-item-completed-percentage-input').val() || 0),
                    requestedAmount: parseFloat($row.find('.line-item-requested-amount').val() || 0),
                    notes: $row.find('.note-btn').data('note') || '',
                    order: $row.index() + 1
                };

                if($row.find('.line-item-description-display').length){
                    lineItem.description = $row.find('.line-item-description-display').text().trim();
                } else if ($row.find('.description-btn').length) {
                    lineItem.description = $row.find('.description-btn').data('note') || '';
                }

                // Calculate requested percent from amount
                if (lineItem.cost > 0 && lineItem.requestedAmount > 0) {
                    lineItem.requestedPercent = (lineItem.requestedAmount / lineItem.cost) * 100;
                } else {
                    lineItem.requestedPercent = 0;
                }

                groupedLineItems[categoryId].push(lineItem);
            });
        });

        return groupedLineItems;
    }

    /**
     * Build categories JSON for API requests
     *
     * @param {jQuery} $container Categories container
     * @param {Object} config Configuration object
     * @returns {Object} Request object
     */
    static buildCategoriesRequest($container, config) {
        const categories = this.extractCategoriesFromDOM($container);
        const mappedCategories = categories.map(cat => this.mapObject(cat, 'category', 'toBackend'));

        const request = {
            categories: mappedCategories
        };

        // Add identifier based on config
        if (config.dataKey && config[config.dataKey]) {
            request[config.dataKey] = config[config.dataKey];
        }

        return request;
    }

    /**
     * Build line items JSON for API requests
     *
     * @param {jQuery} $container Line items container
     * @param {Object} config Configuration object
     * @returns {Object} Request object
     */
    static buildLineItemsRequest($container, config) {
        const lineItems = this.extractLineItemsFromDOM($container);
        const mappedLineItems = {};

        // Map each category's line items
        Object.keys(lineItems).forEach(categoryId => {
            mappedLineItems[categoryId] = lineItems[categoryId].map(
                item => this.mapObject(item, 'lineItem', 'toBackend')
            );
        });

        const request = {
            lineItems: mappedLineItems,
            isDraft: config.isDraft || false
        };

        // Add identifier based on config
        if (config.dataKey && config[config.dataKey]) {
            request[config.dataKey] = config[config.dataKey];
        }

        return request;
    }

    /**
     * Build draw request JSON for API requests
     *
     * @param {Object} formData Form data
     * @param {Object} config Configuration object
     * @returns {Object} Request object
     */
    static buildDrawRequestRequest(formData, config) {
        const drawRequest = this.mapObject(formData, 'drawRequest', 'toBackend');

        // Add identifier
        if (config.lmrid) {
            drawRequest.lmrid = config.lmrid;
        }

        return drawRequest;
    }

    /**
     * Validate object structure
     *
     * @param {Object} object Object to validate
     * @param {string} objectType Type of object
     * @returns {Array} Array of validation errors
     */
    static validateObject(object, objectType) {
        const errors = [];
        const config = this.propertyMaps[objectType];

        if (!config) {
            errors.push(`Unknown object type: ${objectType}`);
            return errors;
        }

        // Check required properties based on object type
        if (objectType === 'category') {
            if (!object.categoryName || object.categoryName.trim() === '') {
                errors.push('Category name is required');
            }
            if (!object.order || object.order <= 0) {
                errors.push('Category order must be positive');
            }
        } else if (objectType === 'lineItem') {
            if (!object.name || object.name.trim() === '') {
                errors.push('Line item name is required');
            }
            if (!object.cost || object.cost <= 0) {
                errors.push('Line item cost must be positive');
            }
            if (object.requestedAmount > object.cost) {
                errors.push('Requested amount cannot exceed total cost');
            }
        }

        return errors;
    }

    /**
     * Sanitize object for safe transmission
     *
     * @param {Object} object Object to sanitize
     * @returns {Object} Sanitized object
     */
    static sanitizeObject(object) {
        if (!object || typeof object !== 'object') {
            return object;
        }

        const sanitized = {};

        Object.keys(object).forEach(key => {
            let value = object[key];

            if (Array.isArray(value)) {
                value = value.map(item => this.sanitizeObject(item));
            } else if (typeof value === 'object' && value !== null) {
                value = this.sanitizeObject(value);
            } else if (typeof value === 'string') {
                // Basic XSS protection
                value = value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            }

            sanitized[key] = value;
        });

        return sanitized;
    }
}

// Export for use in other modules
window.DataMapper = DataMapper;
