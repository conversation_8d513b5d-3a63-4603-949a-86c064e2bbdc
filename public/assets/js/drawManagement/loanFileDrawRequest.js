$(document).ready(function() {
    function handleStatusChange() {
        const selectedValue = $('.statusAction').val();
        if (selectedValue === 'rejected' || currentStatus === 'rejected') {
            $('.col-reject-reason').removeClass('hide');
        } else {
            $('.col-reject-reason').addClass('hide');
        }
        validateSaveButton();
    }

    $('.statusAction').on('change', handleStatusChange);

    // Setup input handlers using shared utility
    DrawRequestUtils.setupInputHandlers(validateSaveButton);

    handleStatusChange();
    initializeLenderNotesModal();
    validateSaveButton();

    $saveBtn.on('click', function(e) {
        e.preventDefault();
        submitDrawManagementData();
    });

    // Handle Export Table button click
    $('#exportTableBtn').on('click', function(e) {
        e.preventDefault();
        exportTableToPdf();
    });
});

function validateSaveButton() {
    const selectedStatus = $('.statusAction').val();

    // check if at least one field has a non-zero value
    if (selectedStatus === statusApproved && sowApproved) {
        const hasNonZeroValue = DrawRequestUtils.hasNonZeroValues();
        $saveBtn.prop('disabled', !hasNonZeroValue);
    } else {
        $saveBtn.prop('disabled', false);
    }
}

// Common functions are now in drawRequest.js

function initializeLenderNotesModal() {
    const $buttons = $('.lender-note-btn');
    if ($buttons.length === 0) {
        console.warn('No lender note buttons found. Check if the table is loaded and buttons have the correct class.');
        return;
    }

    let currentLineItemId = null;
    let currentButton = null;

    $(document).on('click', '.lender-note-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $btn = $(this);
        currentLineItemId = $btn.data('line-item-id');
        currentButton = $btn;
        const currentNote = $btn.find('i').data('original-title') || '';
        $('#lenderNotesTextarea').val(currentNote);
        $('#lenderNotesModal').modal('show');
    });

    $('#saveLenderNote').on('click', function() {
        const newNote = $('#lenderNotesTextarea').val().trim();

        if (currentButton) {
            const $icon = currentButton.find('i');
            $icon.data('original-title', newNote);
            $icon.attr('data-original-title', newNote);

            if (newNote) {
                $icon.removeClass('text-muted').addClass('text-primary');
            } else {
                $icon.removeClass('text-primary').addClass('text-muted');
            }
        }

        $('#lenderNotesModal').modal('hide');
    });

    $('#lenderNotesModal').on('hidden.bs.modal', function() {
        currentLineItemId = null;
        currentButton = null;
        $('#lenderNotesTextarea').val('');
    });
}

function submitDrawManagementData() {
    const status = $('#status').val();
    const lineItems = {};

    // Extract line item data from DOM
    $('.line-item').each(function() {
        const $row = $(this);
        const lineItemId = $row.find('.lender-note-btn').data('line-item-id');

        if (lineItemId) {
            const borrowerNotes = $row.find('input[name="notes"]').val() || '';
            const lenderNotes = $row.find('.lender-note-btn > i').data('original-title') || '';
            const rejectReason = status === 'rejected' ? $row.find('select[name="rejectReason"]').val() : '';

            const lineItemData = {
                id: lineItemId,
                notes: borrowerNotes,
                lenderNotes: lenderNotes,
                rejectReason: rejectReason
            };

            if($('.requested-amount').length) {
                const requestedAmount = parseFloat($row.find('.requested-amount').val()) || 0;
                lineItemData.requestedAmount = requestedAmount;
            }

            lineItems[lineItemId] = lineItemData;
        }
    });

    // Build draw request data using DataMapper
    const drawRequestData = {
        lmrid: lmrid,
        status: status,
        lineItems: lineItems
    };

    // Validate the data using Validator
    const validator = new Validator();
    const validationContext = {
        userType: 'lender',
        isDraft: false
    };

    // Validate draw request structure
    if (!validator.validateForm(drawRequestData, 'drawRequest', validationContext)) {
        const errors = validator.getErrors();
        toastrNotification('Validation Error: ' + errors.join(', '), 'error');
        return;
    }

    // Sanitize data using DataMapper
    const sanitizedData = DataMapper.sanitizeObject(drawRequestData);

    // Show loading state
    $saveBtn.prop('disabled', true).text('Saving...');

    // Use ApiClient for the request
    drawManagementApi.saveDrawRequest(sanitizedData)
        .then(function(response) {
            if (response.success) {
                toastrNotification('Draw Request Status Updated!', 'success');
                setTimeout(function() {
                    location.reload();
                }, 1500);
            } else {
                toastrNotification('Error: ' + (response.message || 'Failed to update Draw Request Status'), 'error');
            }
        })
        .catch(function(error) {
            console.error('API Error:', error);
            let errorMsg = error.message || 'An error occurred while saving data. Please try again.';
            toastrNotification(errorMsg, 'error');
        })
        .always(function() {
            $saveBtn.prop('disabled', false).text('Save');
        });
}

function exportTableToPdf() {
    const $exportBtn = $('#exportTableBtn');
    const originalText = $exportBtn.html();

    // Show loading state
    $exportBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');

    // Prepare data for API call - now we only need the LMR ID
    const exportData = {
        lmrId: lmrid
    };

    // Sanitize data using DataMapper
    const sanitizedData = DataMapper.sanitizeObject(exportData);

    // Use ApiClient for the request
    drawManagementApi.exportToPdf(sanitizedData)
        .then(function(response) {
            if (response.success) {
                // Convert base64 to blob and trigger download
                const byteCharacters = atob(response.data.pdf_data);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], { type: 'application/pdf' });

                // Create download link
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = response.data.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                toastrNotification('PDF exported successfully!', 'success');
            } else {
                toastrNotification('Error: ' + (response.data.message || 'Failed to generate PDF'), 'error');
            }
        })
        .catch(function(error) {
            console.error('PDF Export Error:', error);
            let errorMsg = error.message || 'An error occurred while generating the PDF. Please try again.';
            toastrNotification(errorMsg, 'error');
        })
        .always(function() {
            // Restore button state
            $exportBtn.prop('disabled', false).html(originalText);
        });
}
