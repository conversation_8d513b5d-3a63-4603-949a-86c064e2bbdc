/**
 * Common Draw Management Functionality
 * Shared between lender and borrower interfaces
 */

window.DrawManagement = window.DrawManagement || {};

DrawManagement = {
    config: {
        categoriesModified: false,
        lineItemsModified: false,
        pcid: null,
        lmrid: null,
        maxCategories: 20,
        userType: null, // 'lender' or 'borrower'
        dataKey: null,
        dataUrl: null,
        isDraft: false
    },
    templateSettings: {},
    currentTemplateData: [],
    elements: {},

    /**
     * Initialize common functionality
     * @param {string} userType - 'lender' or 'borrower'
     */
    init: function(userType, settings = {}) {
        this.config.userType = userType;
        this.config.maxCategories = parseInt($('#maxCategories').val()) || 20;
        if (settings.length) this.templateSettings = JSON.parse(settings);

        // Debug: Check if utilities are available
        if (typeof console !== 'undefined' && console.log) {
            console.log('DrawManagement utilities status:', {
                DataMapper: typeof DataMapper !== 'undefined',
                Validator: typeof Validator !== 'undefined',
                ApiClient: typeof ApiClient !== 'undefined',
                drawManagementApi: typeof drawManagementApi !== 'undefined'
            });
        }

        this.elements = {
            $categoriesContainer: $('.categories-container'),
            $lineItemCategoriesContainer: $('#lineItemCategoriesContainer'),
            $saveCatBtn: $('.save-cat'),
            $saveLineItemsBtn: $('.save-line-items'),
            $saveCategoryModalBtn: $('#saveCategoryModalBtn'),
            $openAddCategoryModalBtn: $('#openAddCategoryModalBtn')
        };

        if (this.config.userType === 'lender') {
            this.lender.init();
        } else {
            this.borrower.init();
        }
        this.loadInitialData();
    },

    /**
     * Initialize lender-specific event handlers
     */
    initEventHandlers: function() {
        const self = this;
        self.elements.$openAddCategoryModalBtn.on('click', function() {
            if (!self.checkAddMoreCategories(true)) {
                toastrNotification('Maximum number of categories reached.', 'warning');
                return;
            }
            self.openCategoryModal('add');
        });

        self.elements.$categoriesContainer.on('click', '.edit-category-btn', function(e) {
            e.preventDefault();
            const $categoryItem = $(this).closest('.category-item');
            const categoryData = {
                id: $categoryItem.data('category-id'),
                name: $categoryItem.data('name'),
                description: $categoryItem.data('description')
            };
            self.openCategoryModal('edit', categoryData);
        });

        self.elements.$saveCategoryModalBtn.on('click', function() {
            self.saveCategoryFromModal();
        });

        self.elements.$categoriesContainer.on('click', '.delete-category-btn', async function(e) {
            e.preventDefault();
            const $categoryItem = $(this).closest('.category-item');
            const confirm = await self.showConfirmationModal('Are you sure you want to delete this category?');
            if (confirm) {
                self.deleteCategory($categoryItem);
            }
        });

        self.elements.$saveCatBtn.on('click', function() {
            self.saveCategories(this);
        });
    },

    /**
     * Save category from modal
     */
    saveCategoryFromModal: function() {
        if (!this.validateCategoryForm()) {
            return;
        }

        const categoryId = $('#modalCategoryId').val();
        const name = $('#modalCategoryName').val().trim();
        const description = $('#modalCategoryDescription').val().trim();
        const template = document.getElementById('category-item-template');

        if (categoryId) {
            const $categoryItem = $(`.category-item[data-category-id="${categoryId}"]`);
            if ($categoryItem.length) {
                $categoryItem.find('.category-name').text(name);
                $categoryItem.find('.category-description').text(description);
                $categoryItem.data('name', name);
                $categoryItem.data('description', description);
                $categoryItem.attr('data-name', name);
                $categoryItem.attr('data-description', description);
            }
        } else {
            if (!this.checkAddMoreCategories(true)) return;

            const newId = 'new_cat_' + Date.now();
            const clone = template.content.cloneNode(true);
            const $newCategoryItem = $(clone).find('.category-item');

            $newCategoryItem.attr('data-category-id', newId);
            $newCategoryItem.attr('data-name', name);
            $newCategoryItem.attr('data-description', description);
            $newCategoryItem.find('.category-name').text(name);
            $newCategoryItem.find('.category-description').text(description);

            this.elements.$categoriesContainer.find('.no-cat').remove();
            this.elements.$categoriesContainer.append($newCategoryItem);
        }

        this.config.categoriesModified = true;
        this.elements.$saveCatBtn.prop('disabled', false);
        $('#categoryFormModal').modal('hide');
        this.checkAddMoreCategories();
    },

    /**
     * Save categories
     */
    saveCategories: function(btn) {
        const categoriesToSave = this.buildCategoriesJson();
        const $saveBtn = $(btn);
        const btnText = $saveBtn.text();
        const self = this;

        if (!self.config[self.config.dataKey]) {
            toastrNotification(`Error: ${self.config.dataKey.toUpperCase()} is missing.`, 'error');
            return;
        }

        // Validate data before sending if Validator is available
        const validator = new Validator();
        if (!validator.validateForm(categoriesToSave, 'categories', self.config)) {
            toastrNotification('Validation failed: ' + validator.getFirstError(), 'error');
            return;
        }

        this.elements.$saveCatBtn.prop('disabled', true);
        $saveBtn.text('Saving...');

        drawManagementApi.saveCategories(categoriesToSave, self.config.userType)
            .then(function(response) {
                if (response.success && response.data) {
                    self.currentTemplateData = response.data;
                    self.renderCategoriesUI();
                    self.config.categoriesModified = false;
                    toastrNotification('Categories saved successfully!', 'success');
                    self.elements.$saveCatBtn.prop('disabled', true);
                } else {
                    toastrNotification('Error saving categories: ' + (response.message || 'Unknown error'), 'error');
                    self.elements.$saveCatBtn.prop('disabled', false);
                }
            })
            .catch(function(error) {
                toastrNotification('Error saving categories: ' + error.message, 'error');
                self.elements.$saveCatBtn.prop('disabled', false);
            })
            .always(function() {
                $saveBtn.text(btnText);
                if (self.config.categoriesModified == false && $saveBtn.attr('id') === 'save-next') {
                    self.switchStep('line-items');
                }
            });
    },

    /**
     * Delete category
     */
    deleteCategory: function($categoryItem) {
        $categoryItem.remove();
        this.config.categoriesModified = true;
        this.elements.$saveCatBtn.prop('disabled', false);
        if (this.elements.$categoriesContainer.find('.category-item').length === 0) {
            this.elements.$categoriesContainer.append('<p class="no-cat">No categories added. Click "Add Category" to begin.</p>');
        }
        this.checkAddMoreCategories();
    },

    /**
     * Save line items
     */
    saveLineItems: function() {
        const self = this;
        const lineItemsJson = self.buildLineItemsJson();

        // Validate data before sending if Validator is available
        const validator = new Validator();
        if (!validator.validateForm(lineItemsJson, 'lineItems', self.config)) {
            toastrNotification('Validation failed: ' + validator.getFirstError(), 'error');
            return;
        }

        // Use API client for consistent request handling if available
        drawManagementApi.saveLineItems(lineItemsJson, self.config.userType)
            .then(response => {
                if (response.success) {
                    toastrNotification(self.config.saveLineItemsSuccessMessage, 'success');
                    self.config.lineItemsModified = false;
                    self.elements.$saveLineItemsBtn.prop('disabled', true);
                    if (self.config.userType === 'borrower') {
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    }
                } else {
                    toastrNotification('Error saving line items: ' + (response.message || 'Unknown error'), 'error');
                    self.elements.$saveLineItemsBtn.prop('disabled', false);
                }
            })
            .catch(error => {
                toastrNotification('Error saving line items: ' + error.message, 'error');
                self.elements.$saveLineItemsBtn.prop('disabled', false);
        });
    },

    buildLineItemsJson: function() {
        return DataMapper.buildLineItemsRequest(
            DrawManagement.elements.$lineItemCategoriesContainer,
            DrawManagement.config
        );
    },

    /**
     * Initialize stepper functionality
     */
    initStepper: function() {
        const steps = document.querySelectorAll('.step');
        const self = this;
        steps.forEach(step => {
            step.addEventListener('click', function() {
                self.switchStep(this);
            });
        });

        $('.switch-step').on('click', function() {
            const targetStepName = $(this).data('target-step');
            self.switchStep(targetStepName);
        });
    },

    /**
     * Initialize sortable functionality
     */
    initSortable: function() {
        const self = this;
        let sortableEnabled = true;

        if (self.config.userType === 'borrower' && self.borrower && self.borrower.hasPermission) {
            sortableEnabled = self.borrower.hasPermission('allowBorrowersAddEditCategories');
        }

        if (sortableEnabled) {
            new Sortable(this.elements.$categoriesContainer[0], {
                animation: 150,
                ghostClass: 'sortable-ghost',
                handle: '.category-item',
                onEnd: function(evt) {
                    self.config.categoriesModified = true;
                    self.elements.$saveCatBtn.prop('disabled', false);
                }
            });
        }
    },

    /**
     * Load initial data
     */
    loadInitialData: function() {
        if (document.getElementById('content-step-categories')) {
            document.getElementById('content-step-categories').style.display = 'block';
            this.fetchAndRenderCategories().then(() => {
                this.checkAddMoreCategories();
                if (this.config.userType === 'lender') {
                    this.lender.initTemplateSettings();
                }
                if (this.config.userType === 'borrower' && this.currentTemplateData.status !== 'new') {
                    this.switchStep('line-items');
                }
                this.initStepper();
                this.initEventHandlers();
                this.initLineItemEventHandlers();
                this.initSortable();
            });
        }
    },

    /**
     * Switch between stepper steps
     * @param {string|Element} stepElementOrName - Step element or step name
     */
    switchStep: async function(stepElementOrName) {
        let targetStepName;
        let stepElement;
        const self = this;

        if (typeof stepElementOrName === 'string') {
            targetStepName = stepElementOrName;
            stepElement = document.querySelector(`.stepper-container .step[data-step="${targetStepName}"]`);
        } else if (stepElementOrName && stepElementOrName.jquery) {
            stepElement = stepElementOrName[0];
            targetStepName = $(stepElementOrName).data('step');
        } else {
            stepElement = stepElementOrName;
            targetStepName = stepElement.dataset.step;
        }

        if (!stepElement) return;
        if (stepElement.classList.contains('active')) return;

        if (this.config.categoriesModified || this.config.lineItemsModified) {
            const confirm = await self.showConfirmationModal(
                'You have unsaved changes. Are you sure you want to leave without saving?'
            );
            if (!confirm) return;
        }

        document.querySelectorAll('.stepper-container .step').forEach(s => s.classList.remove('active'));
        document.querySelectorAll('.step-content > div').forEach(content => content.style.display = 'none');

        stepElement.classList.add('active');
        const targetContent = document.getElementById(`content-step-${targetStepName}`);
        if (targetContent) {
            targetContent.style.display = 'block';
        } else {
            stepElement.classList.remove('active');
            return;
        }

        if (targetStepName === 'line-items') {
            this.fetchLineItemsData();
        } else if (targetStepName === 'categories') {
            this.fetchAndRenderCategories().then(() => {
            this.checkAddMoreCategories();
            });
        }

        this.config.categoriesModified = false;
        this.config.lineItemsModified = false;
    },

    /**
     * Show confirmation modal
     * @param {string} message - Confirmation message
     * @param {string} confirmButtonText - Confirm button text
     * @param {string} cancelButtonText - Cancel button text
     * @returns {Promise<boolean>} - User's choice
     */
    showConfirmationModal: function(message, confirmButtonText = "Confirm", cancelButtonText = "Cancel") {
        return new Promise((resolve) => {
            const modalElement = $('#confirmationModal');
            const modalBodyElement = $('#confirmationModalBody');
            const confirmBtn = $('#confirmModalConfirmBtn');
            const cancelBtn = $('#confirmModalCancelBtn');

            modalBodyElement.html(message);
            confirmBtn.text(confirmButtonText);
            cancelBtn.text(cancelButtonText);

            confirmBtn.off('click');
            cancelBtn.off('click');
            modalElement.off('hidden.bs.modal');

            confirmBtn.on('click', () => {
                modalElement.modal('hide');
                resolve(true);
            });

            cancelBtn.on('click', () => {
                resolve(false);
            });

            modalElement.on('hidden.bs.modal', () => {
                resolve(false);
            });

            modalElement.modal('show');
        });
    },

    /**
     * Form validation for category modal
     * @returns {boolean} - Is form valid
     */
    validateCategoryForm: function() {
        let isValid = true;
        const $nameField = $('#modalCategoryName');
        $nameField.removeClass('is-invalid').siblings('.invalid-feedback').hide();

        if ($nameField.val().trim() === '') {
            $nameField.addClass('is-invalid').siblings('.invalid-feedback').show();
            isValid = false;
        }
        return isValid;
    },

    /**
     * Open category modal
     * @param {string} mode - 'add' or 'edit'
     * @param {Object} categoryData - Category data for edit mode
     */
    openCategoryModal: function(mode, categoryData = {}) {
        const $modal = $('#categoryFormModal');
        const $form = $('#categoryForm');
        $form[0].reset();
        $('#modalCategoryName').removeClass('is-invalid').siblings('.invalid-feedback').hide();

        if (mode === 'add') {
            $('#categoryModalLabel').text('Add New Category');
            this.elements.$saveCategoryModalBtn.text('Add Category');
            $('#modalCategoryId').val('');
        } else if (mode === 'edit' && categoryData.id) {
            $('#categoryModalLabel').text('Edit Category');
            this.elements.$saveCategoryModalBtn.text('Save Changes');
            $('#modalCategoryId').val(categoryData.id);
            $('#modalCategoryName').val(categoryData.name);
            $('#modalCategoryDescription').val(categoryData.description);
        } else {
            console.error("Invalid mode or missing data for category modal.");
            return;
        }
        $modal.modal('show');
    },

    /**
     * Check if more categories can be added
     * @param {boolean} showAlert - Show alert if limit reached
     * @returns {boolean} - Can add more categories
     */
    checkAddMoreCategories: function(showAlert = true) {
        let numCategories = this.elements.$categoriesContainer.children('.category-item').length;
        if (numCategories >= this.config.maxCategories) {
            this.elements.$openAddCategoryModalBtn.prop('disabled', true).attr('title', 'Maximum number of categories reached.');
            if (showAlert) toastrNotification('Maximum number of categories (' + this.config.maxCategories + ') reached.', 'error');
            return false;
        }
        this.elements.$openAddCategoryModalBtn.prop('disabled', false).attr('title', '');
        return true;
    },

    /**
     * Build categories JSON for saving
     * @returns {Object} - Categories request data
     */
    buildCategoriesJson: function() {
        // Use DataMapper for consistent data extraction and mapping if available
        return DataMapper.buildCategoriesRequest(
            this.elements.$categoriesContainer,
            this.config
        );
    },

    /**
     * Save line item inline edit
     */
    saveLineItemInlineEdit: function($input, e) {
        e.preventDefault();
        const $display = $input.siblings('.line-item-display');
        const newValue = $input.val().trim();
        const $row = $input.closest('tr');
        const lineItemId = $row.data('line-item-id');

        if ($input.hasClass('line-item-name-input') && newValue === '') {
            if (typeof lineItemId === 'string' && lineItemId.startsWith('new_li_')) {
                $row.remove();
            } else {
                $input.addClass('is-invalid');
                $input.attr('placeholder', 'Name can not be empty.');
            }
            return;
        }

        if (typeof lineItemId === 'string' && lineItemId.startsWith('new_li_')) {
            if ($input.hasClass('line-item-name-input')) $row.data('new-name', newValue);
            if ($input.hasClass('line-item-description-input')) $row.data('new-description', newValue);
        }
        if($display.length) {
            $display.text(newValue).removeClass('d-none');
            $input.addClass('d-none').removeClass('is-invalid');
        }
        this.config.lineItemsModified = true;
        this.elements.$saveLineItemsBtn.prop('disabled', false);
    },

    /**
     * Add new line item
     */
    addNewLineItem: function($addRowClicked) {
        const categoryId = $addRowClicked.closest('.add-line-item-row').data('category-id');
        const $tbody = $addRowClicked.closest('table').find('tbody.sortable');
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) return;

        const clone = rowTemplate.content.cloneNode(true);
        const $newRow = $(clone).find('tr');
        const newLineItemId = 'new_li_' + Date.now();
        $newRow.attr('data-line-item-id', newLineItemId).attr('data-category-id', categoryId);
        $tbody.append($newRow);
        const $firstInput = $newRow.find('.line-item-name-input');
        $firstInput.siblings('.line-item-display').addClass('d-none');
        $firstInput.removeClass('d-none').focus();
        this.config.lineItemsModified = true;
        this.elements.$saveLineItemsBtn.prop('disabled', false);
    },

    /**
     * Remove line item
     */
    removeLineItem: function($row) {
        $row.remove();
        this.config.lineItemsModified = true;
        this.elements.$saveLineItemsBtn.prop('disabled', false);
    },

    /**
     * Initialize line item event handlers
     */
    initLineItemEventHandlers: function() {
        const self = this;
        self.elements.$lineItemCategoriesContainer.on('click', '.editable-line-item td:not(:last-child)', function() {
            const $td = $(this);
            $td.closest('tr').find('.line-item-input:visible').not($td.find('.line-item-input')).each(function() {
                $(this).trigger($.Event('keypress', { which: 13 }));
            });
            $td.find('.line-item-display').addClass('d-none');
            $td.find('.line-item-input').removeClass('d-none').focus();
        });

        self.elements.$lineItemCategoriesContainer.on('focusout keypress', 'input', function(e) {
            if (e.type === 'focusout' || (e.type === 'keypress' && e.which === 13)) {
                self.saveLineItemInlineEdit($(this), e);
            }
        });

        self.elements.$lineItemCategoriesContainer.on('click', '.add-new-line-item-link', function(e) {
            e.preventDefault();
            self.addNewLineItem($(this));
        });

        self.elements.$lineItemCategoriesContainer.on('click', '.remove-line-item-btn', async function(e) {
            e.preventDefault();
            const $row = $(this).closest('tr');
            const confirm = await self.showConfirmationModal('Are you sure you want to delete this line item?');
            if (confirm) {
                self.removeLineItem($row);
            }
        });

        self.elements.$saveLineItemsBtn.on('click', async function() {
            let confirmMsg = 'Are you sure you want to submit the draw request?';
            if($(this).hasClass('save-draft')) {
                self.config.isDraft = true
                confirmMsg = 'Save draw Request as a draft?';
            };
            if (self.config.userType === 'lender') {
                self.saveLineItems();
                return;
            }
            const confirm = await self.showConfirmationModal(confirmMsg);
            if (confirm) self.saveLineItems();
            self.config.isDraft = false;
        });
    },

    /**
     * Fetch and render categories
     */

    fetchAndRenderCategories: function() {
        const self = this;
        return new Promise((resolve, reject) => {
            if (!self.config[self.config.dataKey]) {
                console.error(`${self.config.dataKey.toUpperCase()} is missing. Cannot load categories.`);
                this.elements.$categoriesContainer.html(`<p class="text-danger">Error: Configuration problem (${self.config.dataKey.toUpperCase()} missing). Cannot load categories.</p>`);
                return;
            }

            $.ajax({
                url: self.config.dataUrl,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.data) {
                        self.currentTemplateData = response.data;
                        self.renderCategoriesUI();
                        self.config.categoriesModified = false;
                        self.elements.$saveCatBtn.prop('disabled', true);
                        if (self.config.userType === 'borrower' && self.borrower && self.borrower.applyPermissions) {
                            self.borrower.applyPermissions();
                        }

                        resolve(response.data);
                    } else {
                        console.error('Failed to fetch categories:', response.message);
                        self.elements.$categoriesContainer.html(`<p class="text-danger">Error loading categories: ${response.message || 'Unknown server error'}</p>`);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching categories:', status, error);
                    let errorMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error;
                    self.elements.$categoriesContainer.html(`<p class="text-danger">Error loading categories: ${errorMsg}</p>`);
                }
            });
        });
    },

    /**
     * Render categories UI
     */
    renderCategoriesUI: function() {
        const categories = this.currentTemplateData.categories;
        const template = document.getElementById('category-item-template');
        this.elements.$categoriesContainer.find('.category-item, .no-cat').remove();

        if (!categories || categories.length === 0) {
            this.elements.$categoriesContainer.append('<p class="no-cat">No categories found. Click "Add Category" to begin.</p>');
        } else {
            Object.entries(categories).forEach(([key, category]) => {
                const clone = template.content.cloneNode(true);
                const $categoryItem = $(clone).find('.category-item');

                $categoryItem.attr('data-category-id', category.id);
                $categoryItem.attr('data-name', category.categoryName);
                $categoryItem.attr('data-description', category.description);
                $categoryItem.find('.category-name').text(category.categoryName);
                $categoryItem.find('.category-description').text(category.description);

                this.elements.$categoriesContainer.append($categoryItem);
            });
        }
    },

    /**
     * Fetch line items data
     */
    fetchLineItemsData: function() {
        const self = this;
        return $.ajax({
            url: self.config.dataUrl,
            type: 'GET',
            dataType: 'json'
        }).done(function(response) {
            if (response.success && response.data) {
                self.currentTemplateData = response.data;
                self.elements.$saveLineItemsBtn.prop('disabled', true);
                self.config.lineItemsModified = false;
                self.renderLineItemsForCategoriesUI();

                if (self.config.userType === 'borrower' && self.borrower && self.borrower.applyPermissions) {
                    self.borrower.applyPermissions();
                }
            } else {
                console.error(`Failed to fetch line items:`, response.message);
                self.elements.$lineItemCategoriesContainer.html(`<div class="text-danger text-center">Error loading line items: ${response.message || 'Unknown error'}</div>`);
            }
        }).fail(function(xhr, status, error) {
            console.error(`AJAX error fetching line items:`, error);
            let errorMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error;
            self.elements.$lineItemCategoriesContainer.html(`<div class="text-danger text-center">Error loading line items: ${errorMsg}</div>`);
        });
    },

    /**
     * Render line items for categories UI
     */
    renderLineItemsForCategoriesUI: function() {
        this.elements.$lineItemCategoriesContainer.empty();
        const categories = this.currentTemplateData.categories;
        if (!categories || categories.length === 0) {
            this.elements.$lineItemCategoriesContainer.html('<p class="text-muted text-center mt-5">No categories defined. Please go back to the "Categories" step to add them.</p>');
            return;
        }
        const categoryCardTemplate = document.getElementById('line-item-category-card-template');

        Object.entries(categories).forEach(([key, category]) => {
            const clone = categoryCardTemplate.content.cloneNode(true);
            const $categoryCard = $(clone).find('.line-item-category-section');
            const collapseId = `collapseCategory_${category.id || ('temp' + key)}`;
            const lineItems = category['lineItems'];

            $categoryCard.find('.category-name').text(category.categoryName.toUpperCase());
            $categoryCard.find('.category-description').text(category.description || '');
            $categoryCard.find('.line-item-category-header')
                .attr('data-target', `#${collapseId}`)
                .attr('aria-controls', collapseId);
            const $collapseTarget = $categoryCard.find('.category-collapse-placeholder');
            $collapseTarget.attr('id', collapseId);

            if (key < 5) {
                $collapseTarget.addClass('show');
                $categoryCard.find('.line-item-category-header').attr('aria-expanded', 'true');
            } else {
                $collapseTarget.removeClass('show');
                $categoryCard.find('.line-item-category-header').attr('aria-expanded', 'false');
            }

            $categoryCard.attr('data-category-id', category.id);
            $categoryCard.find('.line-items-tbody').attr('data-category-id', category.id);
            $categoryCard.find('.add-line-item-row').attr('data-category-id', category.id);

            this.elements.$lineItemCategoriesContainer.append($categoryCard);
            this.renderLineItemRowsUI(lineItems, $categoryCard.find('.line-items-tbody'));
        });

        this.initSortableLineItems();
    },

    /**
     * Render line item rows UI
     */
    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        $tbodyElement.empty();

        if(this.config.userType === 'lender') {
            this.lender.renderLineItemRowsUI(lineItems, $tbodyElement);
        } else {
            this.borrower.renderLineItemRowsUI(lineItems, $tbodyElement);
        }

        this.config.lineItemsModified = false;
        this.elements.$saveLineItemsBtn.prop('disabled', true);
    },

    initSortableLineItems: function() {
        const sortableLineItemLists = document.querySelectorAll('#lineItemCategoriesContainer .line-item-table tbody.sortable');
        const self = this;

        let sortableEnabled = true;
        if (self.config.userType === 'borrower') {
            sortableEnabled = self.borrower.hasPermission('allowBorrowersAddEditLineItems');
        }

        sortableLineItemLists.forEach(function(listEl) {
            if (listEl.sortableInstance) {
                listEl.sortableInstance.destroy();
            }

            if (sortableEnabled) {
                listEl.sortableInstance = new Sortable(listEl, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    filter: '.sortable-disabled',
                    onEnd: function(evt) {
                        self.config.lineItemsModified = true;
                        self.elements.$saveLineItemsBtn.prop('disabled', false);
                    }
                });
            }
        });
    }
};
