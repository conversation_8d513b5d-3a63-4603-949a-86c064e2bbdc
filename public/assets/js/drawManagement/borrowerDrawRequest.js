$(document).ready(function() {
    let $currentNoteBtn;
    let $saveBtn = $('#btnSave');

    // Handle modal opening
    $('#noteModal').on('show.bs.modal', function (event) {
        $currentNoteBtn = $(event.relatedTarget);
        const noteText = $currentNoteBtn.data('note') || '';
        $('#noteTextarea').val(noteText);
    });

    // Handle save note button
    $('#saveNoteBtn').on('click', function () {
        const updatedNote = $('#noteTextarea').val();

        // Update button data
        $currentNoteBtn.data('note', updatedNote);
        $currentNoteBtn.attr('data-note', updatedNote);

        // Update the popover content
        const popover = $currentNoteBtn.siblings('.popover');
        popover.text(updatedNote);

        // Update icon color based on content
        const icon = $currentNoteBtn.find('i');
        if (updatedNote.trim()) {
            icon.removeClass('text-muted').addClass('text-primary');
        } else {
            icon.removeClass('text-primary').addClass('text-muted');
        }

        $('#noteModal').modal('hide');

        // If DrawManagement object exists, mark as modified
        if (typeof DrawManagement !== 'undefined') {
            DrawManagement.config.lineItemsModified = true;
            DrawManagement.elements.$saveLineItemsBtn.prop('disabled', false);
        }
    });

    // Initialize icon colors based on existing notes
    $('.note-btn').each(function() {
        const noteText = $(this).data('note') || '';
        const icon = $(this).find('i');
        if (noteText.trim()) {
            icon.addClass('text-primary');
        } else {
            icon.addClass('text-muted');
        }
    });

    function validateSubmitButton() {
        // Use shared utility functions
        const hasNonZeroValue = DrawRequestUtils.hasNonZeroValues();
        const hasValidationErrors = DrawRequestUtils.hasValidationErrors();
        $saveBtn.prop('disabled', !hasNonZeroValue || hasValidationErrors);
    }

    // Setup input handlers using shared utility
    DrawRequestUtils.setupInputHandlers(validateSubmitButton);

    // Initial validation of submit button
    validateSubmitButton();

    // Handle submit button click
    $('#btnSave').on('click', function(e) {
        e.preventDefault();

        // Validate all inputs before submitting using shared utility
        if (!DrawRequestUtils.validateAllInputs()) {
            toastrNotification('Please fix validation errors before submitting.', 'error');
            return;
        }

        const lineItemsData = {};
        const lmrid = $('#lmrid').val();
        const status = 'pending';

        // Extract line item data from DOM
        $('.line-item').each(function() {
            const $row = $(this);
            const $amountInput = $row.find('.requested-amount');
            const $noteBtn = $row.find('.note-btn');

            if ($amountInput.length > 0) {
                const lineItemId = $amountInput.data('line-item-id');
                const requestedAmount = parseFloat($amountInput.val()) || 0;
                const notes = $noteBtn.data('note') || '';

                // Build line item data structure
                const lineItemData = {
                    id: lineItemId,
                    requestedAmount: requestedAmount,
                    notes: notes
                };

                // Map using DataMapper for consistency
                lineItemsData[lineItemId] = DataMapper.mapObject(lineItemData, 'lineItem', 'toBackend');
            }
        });

        // Build draw request data
        const drawRequestData = {
            lmrid: lmrid,
            status: status,
            lineItems: lineItemsData
        };

        // Validate the data using Validator
        const validator = new Validator();
        const validationContext = {
            userType: 'borrower',
            isDraft: false
        };

        // Validate line items form
        if (!validator.validateForm(drawRequestData, 'lineItems', validationContext)) {
            const errors = validator.getErrors();
            toastrNotification('Validation Error: ' + errors.join(', '), 'error');
            return;
        }

        // Sanitize data using DataMapper
        const sanitizedData = DataMapper.sanitizeObject(drawRequestData);

        // Show loading state
        $saveBtn.prop('disabled', true).text('Submitting...');

        // Use ApiClient for the request
        drawManagementApi.saveDrawRequest(sanitizedData)
            .then(function(response) {
                if (response.success) {
                    toastrNotification('Draw request submitted successfully!', 'success');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    toastrNotification(response.message || 'Failed to submit draw request.', 'error');
                }
            })
            .catch(function(error) {
                console.error('API Error:', error);
                let errorMsg = error.message || 'An error occurred while submitting the draw request.';
                toastrNotification(errorMsg, 'error');
            })
            .always(function() {
                $saveBtn.prop('disabled', false).text('Submit');
            });
    });
});
