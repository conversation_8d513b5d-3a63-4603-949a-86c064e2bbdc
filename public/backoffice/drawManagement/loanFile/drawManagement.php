<?php

use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\DrawRequest;
use models\PageVariables;
use models\standard\Strings;

global $LMRId;

try {
    $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
    $requestData = $drawRequestManager->getDrawRequestDataArray();
    $categoriesData = [];

    if(isset($requestData['status']) && $requestData['status'] !== DrawRequest::STATUS_NEW ) {
        $categoriesData = $requestData['categories'];
    }
} catch (Exception $e) {
    echo "Error loading draw request data: " . htmlspecialchars($e->getMessage()) . " -->";
    return;
}

$displayStatus = '';
$displayStatusClass = '';
$disableAction = false;
if($requestData && $requestData['status']) {
    switch($requestData['status']) {
        case DrawRequest::STATUS_NEW:
            $disableAction = true;
            break;
        case DrawRequest::STATUS_PENDING:
            $displayStatus = 'Submitted';
            $displayStatusClass = 'bg-warning';
            break;
        case DrawRequest::STATUS_APPROVED:
            $displayStatus = $requestData['isDrawRequest'] ? 'Draw Request Approved' : 'Draw 1 Pending';
            $displayStatusClass = 'bg-success';
            $disableAction = true;
            break;
        case DrawRequest::STATUS_REJECTED:
            $displayStatus = 'Rejected';
            $displayStatusClass = 'bg-danger';
            $disableAction = true;
            break;
    }
}

$disableAction = $disableAction || !PageVariables::$allowToManageDraws;

Strings::includeMyScript([
    '/assets/js/drawManagement/utils/ApiClient.js',
    '/assets/js/drawManagement/drawRequest.js',
    '/assets/js/drawManagement/loanFileDrawRequest.js'
]);

?>
<style>
.work-table {
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
}

.work-table thead {
    background: #f3f6f9;
}

.work-table th {
    font-size: 1rem !important;
    color: #495057;
    border: none;
    text-transform: capitalize;
}

.work-table td {
    padding: 1rem 0.75rem;
    border: none;
}

.work-table tbody tr:hover {
    background-color: #f8f9ff;
    transition: all 0.2s ease;
}

.category-header {
    background: #e1f0ff;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    padding: 0.75rem 1rem;
}

.line-item {
    border-bottom: 1px solid #ebedf3 !important;
}

.line-item td:first-child {
    font-weight: 600;
}

.percentage {
    font-weight: 600;
    font-size: 0.9rem;
    color: #fff;
    text-align: center;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
}

.tooltipClass {
    font-size: 1rem;
}

.note-btn {
    display: contents;
    padding: 5px 7px !important;
    margin-left: calc(0.5rem - 0.25rem);
}

.note-btn i {
    padding: 0 !important;
}

.lender-note-btn {
    display: contents;
    padding: 0 !important;
    margin-left: calc(0.5rem - 0.25rem);
}

.lender-note-btn i {
    padding: 0 !important;
}

.lender-notes-modal .modal-dialog {
    max-width: 500px;
}

.lender-notes-modal .modal-body {
    padding: 20px;
}

.lender-notes-textarea {
    width: 100%;
    min-height: 120px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
    resize: vertical;
    font-family: inherit;
}

.lender-notes-textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.lender-notes-modal .modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

.lender-notes-modal .modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
}

.lender-notes-modal .modal-title {
    font-size: 1.1rem;
    font-weight: 600;
}
span.badge {
    font-size: 1rem;
}
.col-reject-reason {
    padding: 0 !important;
}

/* History table styles */
.history-table {
    margin-top: 2rem;
}

.history-table h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.history-table .badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.history-table .badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.history-table .badge-success {
    background-color: #28a745;
    color: #fff;
}

.history-table .badge-danger {
    background-color: #dc3545;
    color: #fff;
}

.history-table .text-muted {
    color: #6c757d !important;
    font-style: italic;
}
</style>
<div class="card card-body p-0">
    <div class="card card-custom card-stretch d-flex p-0 drawManagementCard">
        <div class="card-header card-header-tabs-line bg-gray-100">
            <div class="card-title">
                <h3 class="card-label">
                    Draw Management
                </h3>
            </div>
            <div class="card-toolbar">
                <a href="javascript:void(0);"
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                    data-card-tool="toggle" data-section="drawManagementCard" data-toggle="tooltip" data-placement="top"
                    title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
            </div>
        </div>

        <div class="card-body p-2">
            <div class="row">
                <div class="col-12">
                    <div class="row">
                        <div class="d-flex justify-content-start mb-4 mt-2 col-md-6">
                            <?php if($displayStatus) { ?>
                                <span class="badge d-inline-flex align-items-center <?= $displayStatusClass; ?>">
                                    <i class="fas <?= $requestData['status'] === DrawRequest::STATUS_APPROVED ? 'fa-check-circle' : 'fa-info-circle'; ?> mr-2 text-dark"></i>
                                    <?= $displayStatus; ?></span>
                            <?php } ?>
                        </div>
                        <div class="d-flex justify-content-end mb-4 mt-2 col-md-6">
                            <?php if(!$disableAction): ?>
                            <div class="col-md-4 d-flex align-items-end">
                                <label class="mr-2 font-weight-bold" for="status">Action:</label>
                                <select class="form-control input-sm statusAction" name="status" id="status">
                                    <option <?= ($requestData['status'] ?? '') !== DrawRequest::STATUS_REJECTED ? 'selected' : ''; ?> value="<?= DrawRequest::STATUS_APPROVED; ?>">Accept</option>
                                    <option <?= ($requestData['status'] ?? '') === DrawRequest::STATUS_REJECTED ? 'selected' : ''; ?> value="<?= DrawRequest::STATUS_REJECTED; ?>">Reject</option>
                                </select>
                            </div>
                            <?php endif; ?>
                        <div class="d-flex align-items-end">
                            <button id="exportTableBtn" class="btn btn-primary btn-sm" type="button">
                                <i class="fas fa-download mr-2"></i>Export Table
                            </button>
                        </div>
                    </div>
                    </div>
                    <div class="work-table">
                        <?php
                        if($requestData && isset($requestData['sowApproved']) && $requestData['sowApproved']):
                            require 'partials/_table-draw-request.php';
                        else:
                            require 'partials/_table-scope-of-work.php';
                        endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php if(!$disableAction): ?>
        <div class="d-flex justify-content-center btn-sm action-buttons">
            <button type="submit" name="btnSave" id="btnSave" class="btn btn-primary">Save</button>
        </div>
    <?php endif; ?>

    <!-- Summary Section -->
    <?php require 'drawSummary.php'; ?>

    <!-- Draw Request History Table -->
    <?php require 'drawHistory.php'; ?>
</div>

<!-- Lender Notes Modal -->
<?php if(!$disableAction): ?>
<div class="modal fade lender-notes-modal" id="lenderNotesModal" tabindex="-1" role="dialog" aria-labelledby="lenderNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="lenderNotesModalLabel">Lender Notes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <textarea class="form-control lender-notes-textarea" id="lenderNotesTextarea" placeholder="Enter lender notes for this line item..."></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveLenderNote">Save Notes</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<script>
const $saveBtn = $('#btnSave');
const sowApproved = parseInt(<?= isset($requestData['sowApproved']) && $requestData['sowApproved'] ? '1' : '0'; ?>);
const statusApproved = '<?= DrawRequest::STATUS_APPROVED; ?>';
const statusRejected = '<?= DrawRequest::STATUS_REJECTED; ?>';
const lmrid = <?= $LMRId; ?>;
const currentStatus = '<?= $requestData['status'] ?? ''; ?>';
</script>
