<?php

namespace pages\backoffice\api_v2\draw_management\loanFile;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\cypher;
use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\dto\request\SaveDrawRequestRequest;
use models\composite\oDrawManagement\dto\response\ApiResponse;
use models\composite\oDrawManagement\validation\PropertyValidator;
use models\lendingwise\tblFile;

/**
 * Class loanFile
 *
 * API endpoint for updating and fetching draw request data
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class LoanFile extends BackofficePage
{
    /**
     * Handle POST requests to update draw request data
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = file_get_contents("php://input");
        $postData = json_decode($postData, true);

        try {
            $LMRId = $postData['lmrid'] ?? null;
            $LMRId = self::getLMRId($LMRId);

            // Update postData with processed LMRId for DTO creation
            $postData['lmrid'] = $LMRId;

            // Validate that the loan file exists before proceeding
            $loanFile = tblFile::Get(['LMRId' => $LMRId]);
            if (!$loanFile) {
                $response = ApiResponse::error("Loan file with ID {$LMRId} not found.");
                HTTP::ExitJSON($response->toArray());
                return;
            }

            // Create and validate request DTO
            $request = SaveDrawRequestRequest::fromArray($postData, true);

            // Additional validation using PropertyValidator
            $validator = new PropertyValidator();
            if (!$validator->validate($request)) {
                $response = ApiResponse::validationError($validator->getErrors());
                HTTP::ExitJSON($response->toArray());
                return;
            }

            // Perform business rule validation based on request type
            $businessErrors = [];
            if ($request->isSubmission()) {
                $businessErrors = array_merge($businessErrors, $request->validateBorrowerSubmission());
            } elseif ($request->isApproval() || $request->isRejection()) {
                $businessErrors = array_merge($businessErrors, $request->validateLenderAction());
            }

            if (!empty($businessErrors)) {
                $response = ApiResponse::validationError($businessErrors);
                HTTP::ExitJSON($response->toArray());
                return;
            }

            // Sanitize the request data
            $request->sanitizeLineItems();

            $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);

            // Convert DTO to array format for existing manager
            $requestData = $request->toArray();

            $result = $drawRequestManager->saveDrawRequestData($requestData);

            if (!$result) {
                $response = ApiResponse::error("Failed to save draw request data.");
                HTTP::ExitJSON($response->toArray());
                return;
            }

            // Get updated data and create response
            $updatedData = $drawRequestManager->getDrawRequestDataArray();
            $response = ApiResponse::success($updatedData, "Draw request data saved successfully.");

            HTTP::ExitJSON($response->toArray());
        } catch (\InvalidArgumentException $e) {
            $response = ApiResponse::validationError([], $e->getMessage());
            HTTP::ExitJSON($response->toArray());
        } catch (\Exception $e) {
            $response = ApiResponse::error("An error occurred: " . $e->getMessage());
            HTTP::ExitJSON($response->toArray());
        }
    }

    /**
     * Get and validate LMRId from request data
     *
     * @param mixed $LMRId Raw LMRId value
     * @return int Validated LMRId
     */
    public static function getLMRId($LMRId): int
    {
        if ($LMRId && !is_numeric($LMRId)) {
            $LMRId = cypher::myDecryption($LMRId);
        }
        $LMRId = (int)$LMRId;

        if (!$LMRId) {
            $response = ApiResponse::error("LMRId is required.");
            HTTP::ExitJSON($response->toArray());
            return 0; // This won't be reached due to HTTP::ExitJSON
        }
        return $LMRId;
    }
}
