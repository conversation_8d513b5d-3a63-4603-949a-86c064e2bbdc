<?php

namespace pages\backoffice\api_v2\draw_management\borrower\SowCategories;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\cypher;
use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\dto\request\SaveCategoriesRequest;
use models\composite\oDrawManagement\dto\response\ApiResponse;
use models\composite\oDrawManagement\validation\PropertyValidator;
use models\lendingwise\tblFile;

/**
 * Class SowCategories
 *
 * API endpoint for updating and fetching SOW categories
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class SowCategories extends BackofficePage
{
    /**
     * Handle GET requests to fetch SOW categories
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $LMRId = $_GET['lmrid'] ?? null;
        $LMRId = self::getLMRId($LMRId);

        // Validate that the loan file exists before proceeding
        $loanFile = tblFile::Get(['LMRId' => $LMRId]);
        if (!$loanFile) {
            $response = ApiResponse::error("Loan file with ID {$LMRId} not found.");
            HTTP::ExitJSON($response->toArray());
            return;
        }

        try {
            $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
            $sowCategories = $drawRequestManager->getDrawRequestDataArray();

            if (empty($sowCategories)) {
                $response = ApiResponse::success([], "No categories found.");
                HTTP::ExitJSON($response->toArray());
                return;
            }

            // Return the data directly - this ensures the response contains the actual data
            $response = ApiResponse::success($sowCategories, "Categories fetched successfully.");

            HTTP::ExitJSON($response->toArray());
        } catch (\Exception $e) {
            $response = ApiResponse::error("An error occurred: " . $e->getMessage());
            HTTP::ExitJSON($response->toArray());
        }
    }

    /**
     * Handle POST requests to update SOW categories
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = file_get_contents("php://input");
        $postData = json_decode($postData, true);

        try {

            $LMRId = $postData['lmrid'] ?? null;
            $LMRId = self::getLMRId($LMRId);

            // Update postData with processed LMRId for DTO creation
            $postData['lmrid'] = $LMRId;

            // Create and validate request DTO
            $request = SaveCategoriesRequest::fromArray($postData, true);

            // Additional validation using PropertyValidator
            $validator = new PropertyValidator();
            if (!$validator->validate($request)) {
                $response = ApiResponse::validationError($validator->getErrors());
                HTTP::ExitJSON($response->toArray());
                return;
            }

            $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);

            // Convert DTO categories to array format for existing manager
            $categoriesData = [];
            foreach ($request->categories as $category) {
                $categoriesData[] = $category->toArray();
            }

            $result = $drawRequestManager->saveCategories($categoriesData);

            if (!$result) {
                $response = ApiResponse::error("Failed to save categories.");
                HTTP::ExitJSON($response->toArray());
                return;
            }

            // Get updated data and create response
            $updatedData = $drawRequestManager->getDrawRequestDataArray();
            $response = ApiResponse::success($updatedData, "Categories saved successfully.");

            HTTP::ExitJSON($response->toArray());
        } catch (\InvalidArgumentException $e) {
            $response = ApiResponse::validationError([], $e->getMessage());
            HTTP::ExitJSON($response->toArray());
        } catch (\Exception $e) {
            $response = ApiResponse::error("An error occurred: " . $e->getMessage());
            HTTP::ExitJSON($response->toArray());
        }
    }

    public static function getLMRId($LMRId)
    {
        if ($LMRId && !is_numeric($LMRId)) $LMRId = cypher::myDecryption($LMRId);
        $LMRId = (int)$LMRId;

        if (!$LMRId) {
            $response = ApiResponse::error("LMRId is required.");
            HTTP::ExitJSON($response->toArray());
            return;
        }
        return $LMRId;
    }
}
