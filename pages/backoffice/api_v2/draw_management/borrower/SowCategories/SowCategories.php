<?php

namespace pages\backoffice\api_v2\draw_management\borrower\SowCategories;

use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\composite\oDrawManagement\dto\request\SaveCategoriesRequest;

/**
 * Class SowCategories
 *
 * API endpoint for updating and fetching SOW categories
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class SowCategories extends DrawManagementApiBase
{
    /**
     * Handle GET requests to fetch SOW categories
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $LMRId = static::getLMRId($_GET['lmrid'] ?? null);

        static::handleGetRequest(
            $LMRId,
            function($LMRId) {
                // Validate loan file exists
                static::validateLoanFile($LMRId);

                // Get draw request manager and fetch data
                $drawRequestManager = static::getDrawRequestManager($LMRId);
                return $drawRequestManager->getDrawRequestDataArray();
            },
            'Categories fetched successfully',
            'No categories found'
        );
    }

    /**
     * Handle POST requests to update SOW categories
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return; // parseJsonInput handles error response

        static::handlePostRequest(
            SaveCategoriesRequest::class,
            $postData,
            function($dto) {
                $LMRId = static::getLMRId($dto->lmrid);

                // Validate loan file exists
                static::validateLoanFile($LMRId);

                // Get draw request manager
                $drawRequestManager = static::getDrawRequestManager($LMRId);

                // Convert DTO categories to array format for existing manager
                $categoriesData = [];
                foreach ($dto->categories as $category) {
                    $categoriesData[] = $category->toArray();
                }

                // Save data
                $result = $drawRequestManager->saveCategories($categoriesData);

                if (!$result) {
                    return ['success' => false, 'data' => null];
                }

                // Get updated data
                $updatedData = $drawRequestManager->getDrawRequestDataArray();
                return ['success' => true, 'data' => $updatedData];
            },
            'Categories saved successfully',
            'Failed to save categories'
        );
    }
}
