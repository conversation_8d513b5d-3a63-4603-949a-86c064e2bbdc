<?php

namespace models\composite\oDrawManagement\serialization;

use models\composite\oDrawManagement\dto\base\BaseDTO;
use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use models\composite\oDrawManagement\dto\shared\DrawRequestData;
use models\composite\oDrawManagement\dto\response\ApiResponse;
use models\composite\oDrawManagement\dto\response\DrawManagementResponse;

/**
 * Serialization/Deserialization System
 *
 * Handles automated conversion between objects, arrays, and JSON
 * with proper type conversion and validation
 */
class Serializer
{
    /**
     * @var array Type mapping for deserialization
     */
    private static array $typeMap = [
        'category' => CategoryData::class,
        'lineItem' => LineItemData::class,
        'drawRequest' => DrawRequestData::class,
    ];

    /**
     * Serialize object to array
     *
     * @param mixed $object Object to serialize
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public static function toArray($object, bool $includeNulls = true): array
    {
        if ($object === null) {
            return [];
        }

        if (is_array($object)) {
            return self::serializeArray($object, $includeNulls);
        }

        if ($object instanceof BaseDTO) {
            return $object->toArray($includeNulls);
        }

        if (is_object($object) && method_exists($object, 'toArray')) {
            return $object->toArray();
        }

        // Handle standard objects using reflection
        return self::serializeObjectWithReflection($object, $includeNulls);
    }

    /**
     * Serialize array of objects
     *
     * @param array $array Array to serialize
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    private static function serializeArray(array $array, bool $includeNulls): array
    {
        $result = [];

        foreach ($array as $key => $item) {
            if ($item === null && !$includeNulls) {
                continue;
            }

            $result[$key] = self::toArray($item, $includeNulls);
        }

        return $result;
    }

    /**
     * Serialize object using reflection
     *
     * @param object $object Object to serialize
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    private static function serializeObjectWithReflection(object $object, bool $includeNulls): array
    {
        $result = [];
        $reflection = new \ReflectionClass($object);
        $properties = $reflection->getProperties(\ReflectionProperty::IS_PUBLIC);

        foreach ($properties as $property) {
            $propertyName = $property->getName();
            $value = $property->getValue($object);

            if ($value === null && !$includeNulls) {
                continue;
            }

            if (is_array($value)) {
                $result[$propertyName] = self::serializeArray($value, $includeNulls);
            } elseif (is_object($value)) {
                $result[$propertyName] = self::toArray($value, $includeNulls);
            } else {
                $result[$propertyName] = $value;
            }
        }

        return $result;
    }

    /**
     * Serialize object to JSON
     *
     * @param mixed $object Object to serialize
     * @param bool $includeNulls Whether to include null values
     * @param int $flags JSON encoding flags
     * @return string
     */
    public static function toJson($object, bool $includeNulls = true, int $flags = JSON_THROW_ON_ERROR): string
    {
        $array = self::toArray($object, $includeNulls);
        return json_encode($array, $flags);
    }

    /**
     * Deserialize array to specific DTO type
     *
     * @param array $data Array data
     * @param string $dtoClass DTO class name
     * @return BaseDTO
     */
    public static function fromArray(array $data, string $dtoClass): BaseDTO
    {
        if (!class_exists($dtoClass)) {
            throw new \InvalidArgumentException("Class {$dtoClass} does not exist");
        }

        if (!is_subclass_of($dtoClass, BaseDTO::class)) {
            throw new \InvalidArgumentException("Class {$dtoClass} must extend BaseDTO");
        }

        return $dtoClass::fromArray($data);
    }

    /**
     * Deserialize JSON to specific DTO type
     *
     * @param string $json JSON string
     * @param string $dtoClass DTO class name
     * @return BaseDTO
     */
    public static function fromJson(string $json, string $dtoClass): BaseDTO
    {
        $data = json_decode($json, true, 512, JSON_THROW_ON_ERROR);
        return self::fromArray($data, $dtoClass);
    }

    /**
     * Auto-deserialize array based on type hints
     *
     * @param array $data Array data
     * @param string|null $typeHint Type hint for auto-detection
     * @return BaseDTO|array
     */
    public static function autoDeserialize(array $data, ?string $typeHint = null)
    {
        if ($typeHint && isset(self::$typeMap[$typeHint])) {
            return self::fromArray($data, self::$typeMap[$typeHint]);
        }

        // Try to auto-detect type based on data structure
        $detectedType = self::detectType($data);
        if ($detectedType) {
            return self::fromArray($data, $detectedType);
        }

        return $data;
    }

    /**
     * Detect DTO type from data structure
     *
     * @param array $data Array data
     * @return string|null Detected DTO class name
     */
    private static function detectType(array $data): ?string
    {
        // Check for DrawRequest indicators
        if (isset($data['LMRId']) && isset($data['status']) && isset($data['categories'])) {
            return DrawRequestData::class;
        }

        // Check for Category indicators
        if (isset($data['categoryName']) && isset($data['lineItems'])) {
            return CategoryData::class;
        }

        // Check for LineItem indicators
        if (isset($data['name']) && isset($data['cost']) && !isset($data['lineItems'])) {
            return LineItemData::class;
        }

        return null;
    }

    /**
     * Create standardized API response
     *
     * @param mixed $data Response data
     * @param bool $success Success status
     * @param string $message Response message
     * @param array $errors Error messages
     * @return ApiResponse
     */
    public static function createApiResponse(
        $data = null,
        bool $success = true,
        string $message = '',
        array $errors = []
    ): ApiResponse {
        if ($success) {
            return ApiResponse::success($data, $message);
        } else {
            return ApiResponse::error($message, $errors, $data);
        }
    }

    /**
     * Create draw management response
     *
     * @param mixed $data Draw management data
     * @param array $permissions User permissions
     * @param array $summary Summary data
     * @return DrawManagementResponse
     */
    public static function createDrawManagementResponse(
        $data,
        array $permissions = [],
        array $summary = []
    ): DrawManagementResponse {
        if ($data instanceof DrawRequestData) {
            return DrawManagementResponse::fromDrawRequest($data, $permissions, $summary);
        }

        if (is_array($data) && !empty($data)) {
            // Check if it's an array of categories
            $firstItem = reset($data);
            if ($firstItem instanceof CategoryData || (is_array($firstItem) && isset($firstItem['categoryName']))) {
                $categories = [];
                foreach ($data as $item) {
                    if ($item instanceof CategoryData) {
                        $categories[] = $item;
                    } elseif (is_array($item)) {
                        $categories[] = CategoryData::fromArray($item);
                    }
                }
                return DrawManagementResponse::fromCategories($categories, $permissions, $summary);
            }
        }

        return DrawManagementResponse::fromCategories([], $permissions, $summary);
    }

    /**
     * Batch serialize multiple objects
     *
     * @param array $objects Array of objects to serialize
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public static function batchSerialize(array $objects, bool $includeNulls = true): array
    {
        $result = [];

        foreach ($objects as $key => $object) {
            $result[$key] = self::toArray($object, $includeNulls);
        }

        return $result;
    }

    /**
     * Batch deserialize multiple objects
     *
     * @param array $dataArray Array of data arrays
     * @param string $dtoClass DTO class name
     * @return array Array of DTO objects
     */
    public static function batchDeserialize(array $dataArray, string $dtoClass): array
    {
        $result = [];

        foreach ($dataArray as $key => $data) {
            if (is_array($data)) {
                $result[$key] = self::fromArray($data, $dtoClass);
            }
        }

        return $result;
    }
}
